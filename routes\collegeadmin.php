<?php

// College Admin Dashboard
Route::get('/collegeadmin/dashboard', 'CollegeAdmin\DashboardController@index');
Route::get('/ajax/collegeadmin/dashboard/schedule_data', 'CollegeAdmin\Ajax\DashboardAjaxController@getScheduleData');
Route::get('/ajax/collegeadmin/dashboard/room_availability', 'CollegeAdmin\Ajax\DashboardAjaxController@getRoomAvailability');

// Section Management
Route::get('/collegeadmin/section_management', 'CollegeAdmin\SectionController@index')->name('collegeadmin.section.index');
Route::get('/collegeadmin/section_management/archive', 'CollegeAdmin\SectionController@archive')->name('collegeadmin.section.archive');
Route::post('/collegeadmin/section_management/create', 'CollegeAdmin\SectionController@createSectionWithCurriculum')->name('collegeadmin.section.create');
Route::get('/collegeadmin/section_management/view/{section_id}', 'CollegeAdmin\SectionController@viewSection')->name('collegeadmin.section.view');
Route::get('/collegeadmin/section_management/archive/{section_id}', 'CollegeAdmin\SectionController@archiveSection')->name('collegeadmin.section.archive_section');
Route::get('/collegeadmin/section_management/add_all/{section_id}', 'CollegeAdmin\SectionController@addAllCurricula')->name('collegeadmin.section.add_all');
Route::post('/collegeadmin/section_management/update', 'CollegeAdmin\SectionController@updateSection')->name('collegeadmin.section.update');
Route::post('/collegeadmin/section_management/add_selected/{section_id}', 'CollegeAdmin\SectionController@addSelectedCurricula')->name('collegeadmin.section.add_selected');
Route::get('/collegeadmin/section_management/remove_offering/{offering_id}', 'CollegeAdmin\SectionController@removeOffering')->name('collegeadmin.section.remove_offering');

// Section Management AJAX
Route::get('/ajax/collegeadmin/section_management/edit_section', 'CollegeAdmin\Ajax\SectionAjax@editSection');
Route::get('/ajax/collegeadmin/section_management/search', 'CollegeAdmin\Ajax\SectionAjax@searchSectionsWithCurricula');
Route::get('/ajax/collegeadmin/section_management/get_available_curricula', 'CollegeAdmin\Ajax\SectionAjax@getAvailableCurricula');
Route::get('/ajax/collegeadmin/section_management/get_sections', 'CollegeAdmin\Ajax\SectionAjax@getSections');
Route::get('/ajax/collegeadmin/section_management/restore_section', 'CollegeAdmin\Ajax\SectionAjax@restoreSection');

// Faculty Management
Route::get('/collegeadmin/faculty/view', 'CollegeAdmin\FacultyController@index')->name('collegeadmin.faculty.index');
Route::get('/collegeadmin/faculty/add', 'CollegeAdmin\FacultyController@create')->name('collegeadmin.faculty.create');
Route::post('/collegeadmin/faculty/store', 'CollegeAdmin\FacultyController@store')->name('collegeadmin.faculty.store');
Route::get('/collegeadmin/faculty/show/{id}', 'CollegeAdmin\FacultyController@show')->name('collegeadmin.faculty.show');
Route::get('/collegeadmin/faculty/edit/{id}', 'CollegeAdmin\FacultyController@edit')->name('collegeadmin.faculty.edit');
Route::put('/collegeadmin/faculty/update/{id}', 'CollegeAdmin\FacultyController@update')->name('collegeadmin.faculty.update');
Route::delete('/collegeadmin/faculty/destroy/{id}', 'CollegeAdmin\FacultyController@destroy')->name('collegeadmin.faculty.destroy');

// Faculty Loading
Route::get('/collegeadmin/faculty_loading', 'CollegeAdmin\FacultyLoadingController@index')->name('collegeadmin.faculty_loading.index');
Route::get('/collegeadmin/faculty_loading/faculty_loading', 'CollegeAdmin\FacultyLoadingController@faculty_loading');
Route::get('/collegeadmin/faculty_loading/generate_schedule/{instructor}', 'CollegeAdmin\FacultyLoadingController@generateSchedule')->name('collegeadmin.faculty_loading.generate_schedule');
Route::get('/ajax/collegeadmin/faculty_loading/courses_to_load', 'CollegeAdmin\Ajax\FacultyLoadingAjax@coursesToLoad');
Route::get('/ajax/collegeadmin/faculty_loading/current_load', 'CollegeAdmin\Ajax\FacultyLoadingAjax@currentLoad');
Route::get('/ajax/collegeadmin/faculty_loading/add_faculty_load', 'CollegeAdmin\Ajax\FacultyLoadingAjax@addFacultyLoad');
Route::get('/ajax/collegeadmin/faculty_loading/remove_faculty_load', 'CollegeAdmin\Ajax\FacultyLoadingAjax@removeFacultyLoad');

// Course Scheduling
Route::get('/collegeadmin/course_scheduling', 'CollegeAdmin\CourseScheduleController@index')->name('collegeadmin.course_scheduling.index');
Route::get('/ajax/collegeadmin/course_scheduling/get_sections', 'CollegeAdmin\Ajax\CourseScheduleAjax@get_sections');
Route::get('/ajax/collegeadmin/course_scheduling/get_courses_offered', 'CollegeAdmin\Ajax\CourseScheduleAjax@get_courses_offered');
Route::get('/collegeadmin/course_scheduling/schedule/{offering_id}/{section_name}', 'CollegeAdmin\CourseScheduleController@add_schedule')->name('collegeadmin.course_scheduling.add_schedule');
Route::get('/ajax/collegeadmin/course_scheduling/get_rooms_available', 'CollegeAdmin\Ajax\CourseScheduleAjax@get_rooms_available');
Route::post('/collegeadmin/course_scheduling/add_schedule', 'CollegeAdmin\CourseScheduleController@add_schedule_post');
Route::get('/collegeadmin/course_scheduling/remove_schedule/{schedule_id}', 'CollegeAdmin\CourseScheduleController@remove_schedule');
Route::get('/collegeadmin/course_scheduling/attach_schedule/{schedule_id}/{offering_id}', 'CollegeAdmin\CourseScheduleController@attach_schedule');

// New course scheduling routes
Route::get('/ajax/collegeadmin/course_scheduling/get_time_blocks', 'CollegeAdmin\Ajax\CourseScheduleAjax@getTimeBlocks');
Route::get('/ajax/collegeadmin/course_scheduling/get_available_instructors', 'CollegeAdmin\Ajax\CourseScheduleAjax@getAvailableInstructors');
Route::get('/ajax/collegeadmin/course_scheduling/check_conflicts', 'CollegeAdmin\Ajax\CourseScheduleAjax@checkConflicts');
Route::post('/collegeadmin/course_scheduling/generate_schedule', 'CollegeAdmin\CourseScheduleController@generateSchedule')->name('collegeadmin.course_scheduling.generate_schedule');
Route::get('/collegeadmin/course_scheduling/tabular_schedule/{section_name}', 'CollegeAdmin\CourseScheduleController@viewTabularSchedule');
Route::get('/collegeadmin/course_scheduling/remove_batch_schedules', 'CollegeAdmin\CourseScheduleController@removeBatchSchedules');
Route::get('/ajax/collegeadmin/faculty_loading/search_courses', 'CollegeAdmin\Ajax\FacultyLoadingAjax@searchCourses');
Route::get('/ajax/collegeadmin/faculty_loading/get_units_loaded', 'CollegeAdmin\Ajax\FacultyLoadingAjax@get_units_loaded');
Route::get('/ajax/collegeadmin/faculty_loading/override_add', 'CollegeAdmin\Ajax\FacultyLoadingAjax@override_add');

// Curriculum Management
Route::get('/collegeadmin/curriculum', 'CollegeAdmin\CurriculumController@index')->name('collegeadmin.curriculum.index');
Route::get('/collegeadmin/curriculum/create', 'CollegeAdmin\CurriculumController@create')->name('collegeadmin.curriculum.create');
Route::post('/collegeadmin/curriculum/store', 'CollegeAdmin\CurriculumController@store')->name('collegeadmin.curriculum.store');
Route::get('/collegeadmin/curriculum/view_curricula/{program_code}', 'CollegeAdmin\CurriculumController@viewCurricula')->name('collegeadmin.curriculum.view_curricula');
Route::get('/collegeadmin/curriculum/list_curriculum/{program_code}/{curriculum_year}', 'CollegeAdmin\CurriculumController@listCurriculum')->name('collegeadmin.curriculum.list_curriculum');
Route::get('/collegeadmin/curriculum/archive_subject/{id}', 'CollegeAdmin\CurriculumController@archiveSubject')->name('collegeadmin.curriculum.archive_subject');
Route::get('/collegeadmin/curriculum/archived_subjects', 'CollegeAdmin\CurriculumController@archivedSubjects')->name('collegeadmin.curriculum.archived_subjects');
Route::get('/collegeadmin/curriculum/restore_subject/{id}', 'CollegeAdmin\CurriculumController@restoreSubject')->name('collegeadmin.curriculum.restore_subject');
Route::get('/collegeadmin/curriculum/fix_data', 'CollegeAdmin\CurriculumController@fixCurriculumData')->name('collegeadmin.curriculum.fix_data');
Route::post('/collegeadmin/curriculum/edit_curriculum', 'CollegeAdmin\CurriculumController@editCurriculum')->name('collegeadmin.curriculum.edit_curriculum');
Route::get('/ajax/collegeadmin/curriculum/edit_modal', 'CollegeAdmin\Ajax\CurriculumAjax@editModal');

// Room Management
Route::get('/collegeadmin/room_management', 'CollegeAdmin\RoomController@index')->name('collegeadmin.room.index');
Route::post('/collegeadmin/room_management/store', 'CollegeAdmin\RoomController@store')->name('collegeadmin.room.store');
Route::get('/collegeadmin/room_management/edit/{id}', 'CollegeAdmin\RoomController@edit')->name('collegeadmin.room.edit');
Route::put('/collegeadmin/room_management/update/{id}', 'CollegeAdmin\RoomController@update')->name('collegeadmin.room.update');
Route::get('/collegeadmin/room_management/archive/{id}', 'CollegeAdmin\RoomController@archive')->name('collegeadmin.room.archive');
Route::get('/collegeadmin/room_management/archived', 'CollegeAdmin\RoomController@archived')->name('collegeadmin.room.archived');
Route::get('/collegeadmin/room_management/restore/{id}', 'CollegeAdmin\RoomController@restore')->name('collegeadmin.room.restore');
Route::get('/ajax/collegeadmin/room_management/edit_room', 'CollegeAdmin\Ajax\RoomAjax@editRoom');
Route::get('/ajax/collegeadmin/room_management/search', 'CollegeAdmin\Ajax\RoomAjax@searchRooms');
Route::get('/ajax/collegeadmin/room_management/search_archive', 'CollegeAdmin\Ajax\RoomAjax@searchRoomsArchive');

// Reports
Route::get('/collegeadmin/reports/faculty', 'CollegeAdmin\ReportsController@facultyReports')->name('collegeadmin.reports.faculty');
Route::get('/collegeadmin/reports/rooms', 'CollegeAdmin\ReportsController@roomsReports')->name('collegeadmin.reports.rooms');

// Course Offerings
Route::get('/collegeadmin/course_offerings', 'CollegeAdmin\CourseOfferingController@index')->name('collegeadmin.course_offering.index');
Route::get('/collegeadmin/course_offerings/program/{program_code}', 'CollegeAdmin\CourseOfferingController@viewProgramOfferings')->name('collegeadmin.course_offering.program');
Route::get('/collegeadmin/course_offerings/section/{section_id}', 'CollegeAdmin\CourseOfferingController@viewSectionOfferings')->name('collegeadmin.course_offering.section');
Route::get('/collegeadmin/course_offerings/section/add_all/{section_id}', 'CollegeAdmin\CourseOfferingController@addAllCurricula')->name('collegeadmin.course_offering.add_all');
Route::post('/collegeadmin/course_offerings/section/add_selected/{section_id}', 'CollegeAdmin\CourseOfferingController@addSelectedCurricula')->name('collegeadmin.course_offering.add_selected');
Route::get('/collegeadmin/course_offerings/section/remove/{section_id}/{offering_id}', 'CollegeAdmin\CourseOfferingController@removeCurriculum')->name('collegeadmin.course_offering.remove');
Route::patch('/collegeadmin/course_offerings/section/update_semester/{section_id}/{offering_id}', 'CollegeAdmin\CourseOfferingController@updateSemester')->name('collegeadmin.course_offering.update_semester');

// Test route for debugging
Route::get('/collegeadmin/test_data', function () {
    $user = Auth::user();
    $collegeCode = $user->college_code;
    $college = App\College::where('college_code', $collegeCode)->first();

    $programService = new App\Services\ProgramService();
    $programs = $programService->getProgramsByCollege($collegeCode);

    return response()->json([
        'user' => $user,
        'college' => $college,
        'programs' => $programs
    ]);
});

// Test route for section search debugging
Route::get('/collegeadmin/test_section_search', function () {
    $user = Auth::user();
    $collegeCode = $user->college_code;

    $sections = App\CtrSection::where('is_active', 1)
        ->where('college_code', $collegeCode)
        ->get();

    return response()->json([
        'college_code' => $collegeCode,
        'sections_count' => $sections->count(),
        'sections' => $sections,
        'unique_programs' => $sections->pluck('program_code')->unique()->values(),
        'unique_levels' => $sections->pluck('level')->unique()->values()
    ]);
});

// Test route for AJAX endpoint debugging
Route::get('/collegeadmin/test_ajax_endpoint', function () {
    $user = Auth::user();
    $collegeCode = $user->college_code;

    // Simulate the AJAX call
    $program_code = request()->get('program_code', '');
    $level = request()->get('level', '');
    $section_name = request()->get('section_name', '');

    $query = App\CtrSection::where('is_active', 1)
        ->where('college_code', $collegeCode);

    if (!empty($program_code) && $program_code !== 'All Programs') {
        $query->where('program_code', $program_code);
    }

    if (!empty($level) && $level !== 'All Levels') {
        $query->where('level', $level);
    }

    if (!empty($section_name)) {
        $query->where('section_name', 'LIKE', '%' . $section_name . '%');
    }

    $sections = $query->get();

    return response()->json([
        'user_id' => $user->id,
        'college_code' => $collegeCode,
        'filters' => [
            'program_code' => $program_code,
            'level' => $level,
            'section_name' => $section_name
        ],
        'sections_found' => $sections->count(),
        'sections' => $sections,
        'query_sql' => $query->toSql()
    ]);
});

// Course Offerings AJAX
Route::get('/ajax/collegeadmin/course_offerings/get_programs', 'CollegeAdmin\Ajax\CourseOfferingAjax@getPrograms')->name('ajax.collegeadmin.course_offering.get_programs');
Route::get('/ajax/collegeadmin/course_offerings/get_curriculum_years', 'CollegeAdmin\Ajax\CourseOfferingAjax@getCurriculumYears')->name('ajax.collegeadmin.course_offering.get_curriculum_years');
Route::get('/ajax/collegeadmin/course_offerings/get_sections', 'CollegeAdmin\Ajax\CourseOfferingAjax@getSections')->name('ajax.collegeadmin.course_offering.get_sections');
Route::get('/ajax/collegeadmin/course_offerings/get_available_curricula', 'CollegeAdmin\Ajax\CourseOfferingAjax@getAvailableCurricula')->name('ajax.collegeadmin.course_offering.get_available_curricula');
Route::get('/ajax/collegeadmin/course_offerings/get_offered_curricula', 'CollegeAdmin\Ajax\CourseOfferingAjax@getOfferedCurricula')->name('ajax.collegeadmin.course_offering.get_offered_curricula');

// Course Offering Curriculum
Route::get('/collegeadmin/course_offerings/curriculum', 'CollegeAdmin\CourseOfferingCurriculumController@index')->name('collegeadmin.course_offering_curriculum.index');
Route::get('/collegeadmin/course_offerings/curriculum/program/{program_code}', 'CollegeAdmin\CourseOfferingCurriculumController@viewProgramOfferings')->name('collegeadmin.course_offering_curriculum.program');
Route::get('/collegeadmin/course_offerings/curriculum/details/{program_code}/{curriculum_year}', 'CollegeAdmin\CourseOfferingCurriculumController@viewCurriculumDetails')->name('collegeadmin.course_offering_curriculum.details');
Route::get('/collegeadmin/course_offerings/curriculum/section/{section_id}', 'CollegeAdmin\CourseOfferingCurriculumController@viewSectionOfferings')->name('collegeadmin.course_offering_curriculum.section');
Route::get('/collegeadmin/course_offerings/curriculum/section/add_all/{section_id}', 'CollegeAdmin\CourseOfferingCurriculumController@addAllCurricula')->name('collegeadmin.course_offering_curriculum.add_all');
Route::post('/collegeadmin/course_offerings/curriculum/section/add_selected/{section_id}', 'CollegeAdmin\CourseOfferingCurriculumController@addSelectedCurricula')->name('collegeadmin.course_offering_curriculum.add_selected');
Route::get('/collegeadmin/course_offerings/curriculum/section/remove/{section_id}/{offering_id}', 'CollegeAdmin\CourseOfferingCurriculumController@removeCurriculum')->name('collegeadmin.course_offering_curriculum.remove');
Route::patch('/collegeadmin/course_offerings/curriculum/section/update_semester/{section_id}/{offering_id}', 'CollegeAdmin\CourseOfferingCurriculumController@updateSemester')->name('collegeadmin.course_offering_curriculum.update_semester');

// Course Offering Curriculum AJAX
Route::get('/ajax/collegeadmin/course_offerings/curriculum/get_programs', 'CollegeAdmin\Ajax\CourseOfferingCurriculumAjax@getPrograms')->name('ajax.collegeadmin.course_offering_curriculum.get_programs');
Route::get('/ajax/collegeadmin/course_offerings/curriculum/get_curriculum_years', 'CollegeAdmin\Ajax\CourseOfferingCurriculumAjax@getCurriculumYears')->name('ajax.collegeadmin.course_offering_curriculum.get_curriculum_years');
Route::get('/ajax/collegeadmin/course_offerings/curriculum/get_sections', 'CollegeAdmin\Ajax\CourseOfferingCurriculumAjax@getSections')->name('ajax.collegeadmin.course_offering_curriculum.get_sections');
Route::get('/ajax/collegeadmin/course_offerings/curriculum/get_curriculum_details', 'CollegeAdmin\Ajax\CourseOfferingCurriculumAjax@getCurriculumDetails')->name('ajax.collegeadmin.course_offering_curriculum.get_curriculum_details');
Route::get('/ajax/collegeadmin/course_offerings/curriculum/get_available_curricula', 'CollegeAdmin\Ajax\CourseOfferingCurriculumAjax@getAvailableCurricula')->name('ajax.collegeadmin.course_offering_curriculum.get_available_curricula');
Route::get('/ajax/collegeadmin/course_offerings/curriculum/get_section_offerings', 'CollegeAdmin\Ajax\CourseOfferingCurriculumAjax@getSectionOfferings')->name('ajax.collegeadmin.course_offering_curriculum.get_section_offerings');
