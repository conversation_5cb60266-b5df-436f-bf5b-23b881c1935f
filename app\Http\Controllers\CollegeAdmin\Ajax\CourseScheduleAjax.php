<?php

namespace App\Http\Controllers\CollegeAdmin\Ajax;

use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Request as RequestFacade;
use App\CtrSection;
use App\TimeBlock;
use App\CtrRoom;
use App\User;
use App\room_schedules;

class CourseScheduleAjax extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('collegeadmin');
    }

    public function get_sections()
    {
        if (RequestFacade::ajax()) {
            $collegeCode = Auth::user()->college_code;
            $level = Input::get('level');
            $program_code = Input::get('program_code');

            // Log the request parameters for debugging
            \Log::info('Filtering sections for college admin with college_code: ' . $collegeCode . ', level: ' . $level . ', program_code: ' . $program_code);

            // Start with a base query for active sections in this college
            $query = CtrSection::where('is_active', 1)
                ->where('college_code', $collegeCode);

            // Filter by level if provided
            if ($level) {
                $query->where('level', $level);
            }

            // Filter by program_code if provided
            if ($program_code) {
                $query->where('program_code', $program_code);
            }

            // Get the filtered sections
            $sections = $query->get();

            \Log::info('Found ' . $sections->count() . ' sections for college admin');

            return view('collegeadmin.course_schedule.ajax.get_sections', compact('sections'));
        }
    }

    public function get_courses_offered(Request $request)
    {
        if (RequestFacade::ajax()) {
            $collegeCode = Auth::user()->college_code;
            $section_name = Input::get('section_name');
            $level = Input::get('level');
            $program_code = Input::get('program_code');

            // Log the request parameters for debugging
            \Log::info('Loading courses for college admin section: ' . $section_name . ', college: ' . $collegeCode . ', program: ' . $program_code . ', level: ' . $level);

            // Get the section details
            try {
                $section = CtrSection::where('section_name', $section_name)
                    ->where('college_code', $collegeCode)
                    ->first();
                    
                if (!$section) {
                    \Log::warning('Section not found or not accessible: ' . $section_name);
                    return view('collegeadmin.course_schedule.ajax.error', ['message' => 'Section not found or you do not have access to it']);
                }
            } catch (\Exception $e) {
                \Log::error('Error retrieving section ' . $section_name . ': ' . $e->getMessage());
                $section = null;
            }

            // Get offerings for this section and college
            try {
                $offerings = \App\offerings_infos_table::where('section_name', $section_name)
                    ->whereHas('curriculum', function($query) use ($collegeCode) {
                        $query->where('college_code', $collegeCode);
                    })
                    ->with(['curriculum', 'schedules' => function($query) {
                        $query->where('is_active', 1);
                    }])
                    ->get();

                \Log::info('Found ' . $offerings->count() . ' offerings for section ' . $section_name);

                return view('collegeadmin.course_schedule.ajax.get_courses_offered', compact('offerings', 'section_name', 'level', 'program_code'));

            } catch (\Exception $e) {
                \Log::error('Error loading courses for section ' . $section_name . ': ' . $e->getMessage());
                return view('collegeadmin.course_schedule.ajax.error', ['message' => 'Error loading courses: ' . $e->getMessage()]);
            }
        }
    }

    public function get_rooms_available()
    {
        if (RequestFacade::ajax()) {
            $collegeCode = Auth::user()->college_code;
            $day = Input::get('day');
            $time_start = Input::get('time_start');
            $time_end = Input::get('time_end');
            $offering_id = Input::get('offering_id');
            $section_name = Input::get('section_name');
            $day_type = Input::get('day_type');
            $multiple_days = Input::get('multiple_days');

            // Get all rooms for this college
            $all_rooms = CtrRoom::where('college_code', $collegeCode)->get();

            // Get rooms that are already occupied during this time
            $occupied_rooms = [];

            if ($multiple_days) {
                $days = explode(',', $multiple_days);
                foreach ($days as $check_day) {
                    $occupied = room_schedules::where('day', $check_day)
                        ->where('is_active', 1)
                        ->where('college_code', $collegeCode)
                        ->where(function($query) use ($time_start, $time_end) {
                            $query->where(function($q) use ($time_start, $time_end) {
                                $q->where('time_starts', '<', $time_end)
                                  ->where('time_end', '>', $time_start);
                            });
                        })
                        ->pluck('room')
                        ->toArray();
                    
                    $occupied_rooms = array_merge($occupied_rooms, $occupied);
                }
                $occupied_rooms = array_unique($occupied_rooms);
            } else {
                $occupied_rooms = room_schedules::where('day', $day)
                    ->where('is_active', 1)
                    ->where('college_code', $collegeCode)
                    ->where(function($query) use ($time_start, $time_end) {
                        $query->where(function($q) use ($time_start, $time_end) {
                            $q->where('time_starts', '<', $time_end)
                              ->where('time_end', '>', $time_start);
                        });
                    })
                    ->pluck('room')
                    ->toArray();
            }

            // Filter available rooms
            $available_rooms = $all_rooms->filter(function($room) use ($occupied_rooms) {
                return !in_array($room->room, $occupied_rooms);
            });

            // Get available instructors for this college
            $instructors = User::where('accesslevel', 1)
                ->where(function ($query) use ($collegeCode) {
                    $query->where('college_code', $collegeCode)
                        ->orWhereHas('instructorInfo', function ($q) use ($collegeCode) {
                            $q->where('college', $collegeCode);
                        });
                })
                ->get();

            // Return the view with available rooms
            if ($multiple_days) {
                return view('collegeadmin.course_schedule.ajax.get_available_rooms', compact('available_rooms', 'instructors', 'offering_id', 'multiple_days', 'day_type', 'time_start', 'time_end', 'section_name'));
            } else {
                return view('collegeadmin.course_schedule.ajax.get_available_rooms', compact('available_rooms', 'instructors', 'offering_id', 'day', 'day_type', 'time_start', 'time_end', 'section_name'));
            }
        }
    }

    /**
     * Get time blocks for a specific day type
     */
    public function getTimeBlocks()
    {
        if (RequestFacade::ajax()) {
            $dayType = Input::get('day_type');
            $timeBlocks = TimeBlock::getByDayType($dayType);
            return response()->json($timeBlocks);
        }
    }

    /**
     * Get available instructors for a specific time slot
     */
    public function getAvailableInstructors()
    {
        if (RequestFacade::ajax()) {
            $collegeCode = Auth::user()->college_code;
            $day = Input::get('day');
            $time_start = Input::get('time_start');
            $time_end = Input::get('time_end');
            $multiple_days = Input::get('multiple_days');

            // Get all instructors for this college
            $all_instructors = User::where('accesslevel', 1)
                ->where(function ($query) use ($collegeCode) {
                    $query->where('college_code', $collegeCode)
                        ->orWhereHas('instructorInfo', function ($q) use ($collegeCode) {
                            $q->where('college', $collegeCode);
                        });
                })
                ->get();

            // Get instructors that are already occupied during this time
            $occupied_instructors = [];

            if ($multiple_days) {
                $days = explode(',', $multiple_days);
                foreach ($days as $check_day) {
                    $occupied = room_schedules::where('day', $check_day)
                        ->where('is_active', 1)
                        ->where(function($query) use ($time_start, $time_end) {
                            $query->where(function($q) use ($time_start, $time_end) {
                                $q->where('time_starts', '<', $time_end)
                                  ->where('time_end', '>', $time_start);
                            });
                        })
                        ->pluck('instructor')
                        ->toArray();
                    
                    $occupied_instructors = array_merge($occupied_instructors, $occupied);
                }
                $occupied_instructors = array_unique($occupied_instructors);
            } else {
                $occupied_instructors = room_schedules::where('day', $day)
                    ->where('is_active', 1)
                    ->where(function($query) use ($time_start, $time_end) {
                        $query->where(function($q) use ($time_start, $time_end) {
                            $q->where('time_starts', '<', $time_end)
                              ->where('time_end', '>', $time_start);
                        });
                    })
                    ->pluck('instructor')
                    ->toArray();
            }

            // Filter available instructors
            $available_instructors = $all_instructors->filter(function($instructor) use ($occupied_instructors) {
                return !in_array($instructor->id, $occupied_instructors);
            });

            return response()->json($available_instructors->values());
        }
    }

    /**
     * Check for scheduling conflicts
     */
    public function checkConflicts()
    {
        if (RequestFacade::ajax()) {
            $collegeCode = Auth::user()->college_code;
            $day = Input::get('day');
            $time_start = Input::get('time_start');
            $time_end = Input::get('time_end');
            $room = Input::get('room');
            $instructor_id = Input::get('instructor_id');
            $multiple_days = Input::get('multiple_days');

            $conflicts = [];

            $days_to_check = $multiple_days ? explode(',', $multiple_days) : [$day];

            foreach ($days_to_check as $check_day) {
                // Check room conflicts
                $roomConflicts = room_schedules::where('day', $check_day)
                    ->where('room', $room)
                    ->where('is_active', 1)
                    ->where('college_code', $collegeCode)
                    ->where(function($query) use ($time_start, $time_end) {
                        $query->where(function($q) use ($time_start, $time_end) {
                            $q->where('time_starts', '<', $time_end)
                              ->where('time_end', '>', $time_start);
                        });
                    })
                    ->count();

                if ($roomConflicts > 0) {
                    $conflicts[] = [
                        'type' => 'room',
                        'day' => $check_day,
                        'message' => "Room {$room} is already occupied on {$check_day} during this time"
                    ];
                }

                // Check instructor conflicts
                if ($instructor_id) {
                    $instructorConflicts = room_schedules::where('day', $check_day)
                        ->where('instructor', $instructor_id)
                        ->where('is_active', 1)
                        ->where(function($query) use ($time_start, $time_end) {
                            $query->where(function($q) use ($time_start, $time_end) {
                                $q->where('time_starts', '<', $time_end)
                                  ->where('time_end', '>', $time_start);
                            });
                        })
                        ->count();

                    if ($instructorConflicts > 0) {
                        $instructor = User::find($instructor_id);
                        $conflicts[] = [
                            'type' => 'instructor',
                            'day' => $check_day,
                            'message' => "Instructor {$instructor->firstname} {$instructor->lastname} is already scheduled on {$check_day} during this time"
                        ];
                    }
                }
            }

            return response()->json([
                'conflicts' => $conflicts,
                'has_conflicts' => !empty($conflicts)
            ]);
        }
    }
}
