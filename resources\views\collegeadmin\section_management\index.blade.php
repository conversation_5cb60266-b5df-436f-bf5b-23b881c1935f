@extends('vendor.adminlte.collegeadmin_layout.app')

@section('title', 'CLASSMOS - Section Management')

@section('main-content')
<link rel='stylesheet' href='{{asset('plugins/select2/select2.css')}}'>
<section class="content-header">
    <h1><i class="fa fa-cubes"></i>
        Section Management
        <small>{{ $college->college_name }}</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{ url('/collegeadmin/dashboard') }}"><i class="fa fa-dashboard"></i> Home</a></li>
        <li class="active">Section Management</li>
    </ol>
</section>

<section class="content">
    <div class="container-fluid">
        @if(Session::has('success'))
        <div class='col-sm-12'>
            <div class='alert alert-success alert-dismissible'>
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <i class="icon fa fa-check"></i> {{ Session::get('success') }}
            </div>
        </div>
        @endif

        @if(Session::has('error'))
        <div class='col-sm-12'>
            <div class='alert alert-danger alert-dismissible'>
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <i class="icon fa fa-ban"></i> {{ Session::get('error') }}
            </div>
        </div>
        @endif

        <div class='row'>
            <div class='col-sm-5'>
                <div class='box box-solid box-primary'>
                    <div class='box-header with-border'>
                        <h3 class="box-title">New Section with Curriculum</h3>
                    </div>
                    <div class='box-body'>
                        <form action="{{ route('collegeadmin.section.create') }}" method="post">
                            {{ csrf_field() }}

                            <div class='form-group'>
                                <label>College</label>
                                <input type="text" class="form-control" value="{{ $college->college_code }} - {{ $college->college_name }}" readonly>
                            </div>

                            <div class='form-group'>
                                <label>Program</label>
                                <select class="select2 form-control" name="program_code" id="program_code" required>
                                    <option value="">Please Select Program</option>
                                    @foreach($programs as $program)
                                    <option value="{{ $program['program_code'] }}">{{ $program['program_code'] }} - {{ $program['program_name'] }}</option>
                                    @endforeach
                                </select>
                            </div>

                            <div class='form-group'>
                                <label>Level</label>
                                <select name="level" id="level" required class='select2 form-control'>
                                    <option value="">Please Select Level</option>
                                    <option value="1st Year">1st Year</option>
                                    <option value="2nd Year">2nd Year</option>
                                    <option value="3rd Year">3rd Year</option>
                                    <option value="4th Year">4th Year</option>
                                    <option value="5th Year">5th Year</option>
                                </select>
                            </div>

                            <div class='form-group'>
                                <label>Section Name</label>
                                <input id="section_name" required name="section_name" type="text" class="form-control" placeholder="Enter section name">
                            </div>

                            <div class='form-group'>
                                <label>Add Curriculum Subjects</label>
                                <select name="add_curriculum" class="form-control" required>
                                    <option value="yes">Yes - Add all available curriculum subjects</option>
                                    <option value="no">No - Create empty section</option>
                                </select>
                            </div>

                            <div class='form-group'>
                                <button onclick='return confirm("Create this section? This action cannot be undone.")' type='submit' class='btn btn-flat btn-success btn-block'>
                                    <i class="fa fa-save"></i> Create Section
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class='col-sm-7'>
                <div class="box box-default">
                    <div class="box-header with-border">
                        <h3 class="box-title">Section List - {{ $college->college_name }}</h3>
                        <div class="box-tools pull-right">
                            <a href="{{ route('collegeadmin.section.archive') }}" class="btn btn-flat btn-warning">
                                <i class="fa fa-archive"></i> View Archives
                            </a>
                        </div>
                    </div>
                    <div class="box-body">
                        <!-- Search/Filter Section -->
                        <div class="row" style="margin-bottom: 15px;">
                            <div class="col-sm-4">
                                <select class="form-control select2" id="filter_program">
                                    <option value="">All Programs</option>
                                    @foreach($uniquePrograms as $program)
                                    <option value="{{ $program }}">{{ $program }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-sm-3">
                                <select class="form-control select2" id="filter_level">
                                    <option value="">All Levels</option>
                                    @foreach($uniqueLevels as $level)
                                    <option value="{{ $level }}">{{ $level }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-sm-3">
                                <input type="text" class="form-control" id="filter_section" placeholder="Section name...">
                            </div>
                            <div class="col-sm-2">
                                <button class="btn btn-flat btn-primary btn-block" onclick="searchSections()">
                                    <i class="fa fa-search"></i> Search
                                </button>
                                <button class="btn btn-flat btn-warning btn-block" onclick="testFiltering()" style="margin-top: 5px;">
                                    <i class="fa fa-bug"></i> Test
                                </button>
                            </div>
                        </div>

                        <div id="sections_table">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>Program</th>
                                            <th>Level</th>
                                            <th>Section Name</th>
                                            <th>Subjects Count</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if(count($sections) > 0)
                                        @foreach($sections as $section)
                                        <tr>
                                            <td>{{ $section->program_code }}</td>
                                            <td>{{ $section->level }}</td>
                                            <td>{{ $section->section_name }}</td>
                                            <td>
                                                @php
                                                $offeringsCount = \App\offerings_infos_table::where('section_name', $section->section_name)->count();
                                                @endphp
                                                {{ $offeringsCount }}
                                            </td>
                                            <td>
                                                <a href="{{ route('collegeadmin.section.view', $section->id) }}" class="btn btn-flat btn-info btn-sm" title="View Section Details">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                                <a href="{{ route('collegeadmin.section.add_all', $section->id) }}" class="btn btn-flat btn-success btn-sm" title="Add All Available Curriculum Subjects" onclick="return confirm('Add all available curriculum subjects to this section?')">
                                                    <i class="fa fa-plus-circle"></i>
                                                </a>
                                                <button data-toggle="modal" data-target="#editModal" onclick="editSection('{{ $section->id }}')" title="Edit Section" class="btn btn-flat btn-primary btn-sm">
                                                    <i class="fa fa-pencil"></i>
                                                </button>
                                                <a href="{{ route('collegeadmin.section.archive_section', $section->id) }}" class="btn btn-flat btn-danger btn-sm" title="Archive Section" onclick="return confirm('Do you wish to archive this section?')">
                                                    <i class="fa fa-archive"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        @endforeach
                                        @else
                                        <tr>
                                            <td colspan="5" class="text-center">No sections found for {{ $college->college_name }}</td>
                                        </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Edit Section Modal -->
<div id="editModal" class="modal fade" role="dialog">
    <div id='displayedit'></div>
</div>

@endsection

@section('scripts')
<script src='{{asset('plugins/select2/select2.min.js')}}'></script>
<script>
$(document).ready(function() {
    // Initialize Select2 with proper configuration
    $('.select2').select2({
        width: '100%',
        placeholder: function() {
            return $(this).data('placeholder') || 'Select an option';
        },
        allowClear: true
    });

    // Add event listeners for filter dropdowns to trigger search automatically
    // Use a slight delay to ensure Select2 is fully initialized
    setTimeout(function() {
        $('#filter_program, #filter_level').on('change', function() {
            console.log('Filter dropdown changed:', $(this).attr('id'), '=', $(this).val());
            searchSections();
        });

        // Test if elements exist
        console.log('Filter program element exists:', $('#filter_program').length > 0);
        console.log('Filter level element exists:', $('#filter_level').length > 0);
    }, 100);
});

// Auto-fill section name when program is selected
$('#program_code').on('change', function() {
    if (this.value != '' && this.value != 'Please Select Program') {
        $('#section_name').val(this.value + '-');
    } else {
        $('#section_name').val('');
    }
});

function editSection(section_id) {
    var array = {};
    array['section_id'] = section_id;
    $.ajax({
        type: "GET",
        url: "/ajax/collegeadmin/section_management/edit_section",
        data: array,
        success: function(data) {
            $('#displayedit').html(data).fadeIn();
            $('#editModal').modal('show');
        },
        error: function() {
            alert('Error loading section details');
        }
    });
}

function searchSections() {
    var program_code = $('#filter_program').val();
    var level = $('#filter_level').val();
    var section_name = $('#filter_section').val();

    // Debug logging
    console.log('Searching sections with filters:', {
        program_code: program_code,
        level: level,
        section_name: section_name
    });

    $.ajax({
        type: "GET",
        url: "/ajax/collegeadmin/section_management/search",
        data: {
            program_code: program_code,
            level: level,
            section_name: section_name
        },
        beforeSend: function(xhr) {
            console.log('Sending AJAX request...');
            // Ensure AJAX headers are set
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
        },
        success: function(data) {
            console.log('Search successful, updating table');
            console.log('Response data length:', data.length);
            $('#sections_table').html(data);
        },
        error: function(xhr, status, error) {
            console.error('Search error details:', {
                status: status,
                error: error,
                responseText: xhr.responseText,
                statusCode: xhr.status,
                readyState: xhr.readyState
            });

            // Try to parse JSON response for better error details
            try {
                var errorData = JSON.parse(xhr.responseText);
                console.error('Parsed error data:', errorData);
                alert('Error searching sections: ' + (errorData.error || error) + '\nStatus: ' + xhr.status);
            } catch (e) {
                alert('Error searching sections: ' + error + '\nStatus: ' + xhr.status + '\nResponse: ' + xhr.responseText.substring(0, 200));
            }
        }
    });
}

// Search on Enter key
$('#filter_section').keypress(function(e) {
    if (e.which == 13) {
        searchSections();
    }
});

// Test function for debugging
function testFiltering() {
    console.log('=== TESTING FILTERING FUNCTIONALITY ===');

    // Test 1: Check if elements exist
    console.log('Filter program element exists:', $('#filter_program').length > 0);
    console.log('Filter level element exists:', $('#filter_level').length > 0);
    console.log('Filter section element exists:', $('#filter_section').length > 0);

    // Test 2: Check current values
    console.log('Current filter values:', {
        program: $('#filter_program').val(),
        level: $('#filter_level').val(),
        section: $('#filter_section').val()
    });

    // Test 3: Test AJAX endpoint directly
    console.log('Testing AJAX endpoint...');
    $.ajax({
        type: "GET",
        url: "/ajax/collegeadmin/section_management/search",
        data: {
            program_code: 'TEST',
            level: 'TEST',
            section_name: 'TEST'
        },
        beforeSend: function(xhr) {
            console.log('Setting AJAX headers...');
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
        },
        success: function(data) {
            console.log('✓ AJAX endpoint works! Response length:', data.length);
            console.log('Response data:', data.substring(0, 500));
            alert('✓ AJAX endpoint is working! Check console for details.');
        },
        error: function(xhr, status, error) {
            console.error('✗ AJAX endpoint failed:', {
                status: status,
                error: error,
                responseText: xhr.responseText,
                statusCode: xhr.status
            });
            alert('✗ AJAX endpoint failed: ' + error + ' (Status: ' + xhr.status + '). Check console for details.');
        }
    });

    // Test 4: Test with debug parameter
    console.log('Testing AJAX endpoint with debug parameter...');
    $.ajax({
        type: "GET",
        url: "/ajax/collegeadmin/section_management/search",
        data: {
            program_code: '',
            level: '',
            section_name: '',
            debug: '1'
        },
        beforeSend: function(xhr) {
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
        },
        success: function(data) {
            console.log('✓ Debug endpoint works! Response:', data);
            alert('✓ Debug endpoint is working! Check console for JSON response.');
        },
        error: function(xhr, status, error) {
            console.error('✗ Debug endpoint failed:', {
                status: status,
                error: error,
                responseText: xhr.responseText,
                statusCode: xhr.status
            });
            alert('✗ Debug endpoint failed: ' + error + ' (Status: ' + xhr.status + ')');
        }
    });

    // Test 5: Trigger change event manually
    console.log('Triggering change event on filter_program...');
    $('#filter_program').trigger('change');
}
</script>
@endsection
